namespace LexAI.LegalResearch.Domain.ValueObjects;

/// <summary>
/// Document type enumeration
/// </summary>
public enum DocumentType
{
    /// <summary>
    /// Law or statute
    /// </summary>
    Law,

    /// <summary>
    /// Regulation or decree
    /// </summary>
    Regulation,

    /// <summary>
    /// Court decision or jurisprudence
    /// </summary>
    Jurisprudence,

    /// <summary>
    /// Legal doctrine or commentary
    /// </summary>
    Doctrine,

    /// <summary>
    /// Contract template
    /// </summary>
    Contract,

    /// <summary>
    /// Legal form or template
    /// </summary>
    Form,

    /// <summary>
    /// Legal procedure or guide
    /// </summary>
    Procedure,

    /// <summary>
    /// Legal analysis or opinion
    /// </summary>
    Analysis,

    /// <summary>
    /// Legal news or update
    /// </summary>
    News,

    /// <summary>
    /// Other document type
    /// </summary>
    Other
}

/// <summary>
/// Legal domain enumeration
/// </summary>
public enum LegalDomain
{
    /// <summary>
    /// Civil law
    /// </summary>
    Civil,

    /// <summary>
    /// Criminal law
    /// </summary>
    Criminal,

    /// <summary>
    /// Administrative law
    /// </summary>
    Administrative,

    /// <summary>
    /// Constitutional law
    /// </summary>
    Constitutional,

    /// <summary>
    /// Commercial and business law
    /// </summary>
    Commercial,

    /// <summary>
    /// Labor and employment law
    /// </summary>
    Labor,

    /// <summary>
    /// Tax law
    /// </summary>
    Tax,

    /// <summary>
    /// Real estate law
    /// </summary>
    RealEstate,

    /// <summary>
    /// Family law
    /// </summary>
    Family,

    /// <summary>
    /// Intellectual property law
    /// </summary>
    IntellectualProperty,

    /// <summary>
    /// Environmental law
    /// </summary>
    Environmental,

    /// <summary>
    /// Health law
    /// </summary>
    Health,

    /// <summary>
    /// Immigration law
    /// </summary>
    Immigration,

    /// <summary>
    /// International law
    /// </summary>
    International,

    /// <summary>
    /// European Union law
    /// </summary>
    European,

    /// <summary>
    /// Banking and finance law
    /// </summary>
    Banking,

    /// <summary>
    /// Insurance law
    /// </summary>
    Insurance,

    /// <summary>
    /// Technology and data law
    /// </summary>
    Technology,

    /// <summary>
    /// Competition law
    /// </summary>
    Competition,

    /// <summary>
    /// Consumer protection law
    /// </summary>
    Consumer,

    /// <summary>
    /// Other legal domain
    /// </summary>
    Other
}

/// <summary>
/// Document status enumeration
/// </summary>
public enum DocumentStatus
{
    /// <summary>
    /// Document is in draft state
    /// </summary>
    Draft,

    /// <summary>
    /// Document is under review
    /// </summary>
    Review,

    /// <summary>
    /// Document is published and active
    /// </summary>
    Published,

    /// <summary>
    /// Document is archived
    /// </summary>
    Archived,

    /// <summary>
    /// Document is deprecated
    /// </summary>
    Deprecated,

    /// <summary>
    /// Document is deleted
    /// </summary>
    Deleted
}

/// <summary>
/// Document priority enumeration
/// </summary>
public enum DocumentPriority
{
    /// <summary>
    /// Low priority
    /// </summary>
    Low,

    /// <summary>
    /// Normal priority
    /// </summary>
    Normal,

    /// <summary>
    /// High priority
    /// </summary>
    High,

    /// <summary>
    /// Critical priority
    /// </summary>
    Critical
}



/// <summary>
/// Search status enumeration
/// </summary>
public enum SearchStatus
{
    /// <summary>
    /// Search is pending
    /// </summary>
    Pending,

    /// <summary>
    /// Search is in progress
    /// </summary>
    InProgress,

    /// <summary>
    /// Search completed successfully
    /// </summary>
    Completed,

    /// <summary>
    /// Search failed
    /// </summary>
    Failed,

    /// <summary>
    /// Search was cancelled
    /// </summary>
    Cancelled,

    /// <summary>
    /// Search timed out
    /// </summary>
    Timeout
}

/// <summary>
/// Search method enumeration
/// </summary>
public enum SearchMethod
{
    /// <summary>
    /// Keyword-based search
    /// </summary>
    Keyword,

    /// <summary>
    /// Semantic vector search
    /// </summary>
    Semantic,

    /// <summary>
    /// Hybrid search (keyword + semantic)
    /// </summary>
    Hybrid,

    /// <summary>
    /// Full-text search
    /// </summary>
    FullText,

    /// <summary>
    /// Fuzzy search
    /// </summary>
    Fuzzy,

    /// <summary>
    /// Boolean search
    /// </summary>
    Boolean
}

/// <summary>
/// Chunk type enumeration
/// </summary>
public enum ChunkType
{
    /// <summary>
    /// Document title
    /// </summary>
    Title,

    /// <summary>
    /// Document summary
    /// </summary>
    Summary,

    /// <summary>
    /// Document paragraph
    /// </summary>
    Paragraph,

    /// <summary>
    /// Document section
    /// </summary>
    Section,

    /// <summary>
    /// Document article
    /// </summary>
    Article,

    /// <summary>
    /// Document clause
    /// </summary>
    Clause,

    /// <summary>
    /// Document footnote
    /// </summary>
    Footnote,

    /// <summary>
    /// Document table
    /// </summary>
    Table,

    /// <summary>
    /// Document list
    /// </summary>
    List,

    /// <summary>
    /// Legal reasoning or justification
    /// </summary>
    LegalReasoning,

    /// <summary>
    /// Legal definition
    /// </summary>
    Definition,

    /// <summary>
    /// Legal procedure
    /// </summary>
    Procedure,

    /// <summary>
    /// Legal sanction or penalty
    /// </summary>
    Sanction,

    /// <summary>
    /// Other chunk type
    /// </summary>
    Other
}



/// <summary>
/// Query intent enumeration (additional values)
/// </summary>
public enum QueryIntent
{
    /// <summary>
    /// Unknown intent
    /// </summary>
    Unknown,

    /// <summary>
    /// General search
    /// </summary>
    General,

    /// <summary>
    /// Question asking
    /// </summary>
    Question,

    /// <summary>
    /// Search for specific information
    /// </summary>
    Information,

    /// <summary>
    /// Search for legal procedures
    /// </summary>
    Procedure,

    /// <summary>
    /// Search for legal definitions
    /// </summary>
    Definition,

    /// <summary>
    /// Search for case law
    /// </summary>
    CaseSearch,

    /// <summary>
    /// Search for legal text
    /// </summary>
    LegalText,

    /// <summary>
    /// Search for legal forms
    /// </summary>
    Forms,

    /// <summary>
    /// Search for legal analysis
    /// </summary>
    Analysis,

    /// <summary>
    /// Search for legal precedents
    /// </summary>
    Precedent,

    /// <summary>
    /// Search for legal updates
    /// </summary>
    Updates,

    /// <summary>
    /// Comparative legal research
    /// </summary>
    Comparative
}
