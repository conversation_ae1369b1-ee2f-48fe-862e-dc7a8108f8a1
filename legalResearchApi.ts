import axios, { type AxiosResponse } from 'axios';

// Configuration de l'API
const LEGAL_RESEARCH_API_BASE_URL = import.meta.env.VITE_LEGAL_RESEARCH_API_URL || 'http://localhost:5002';

// Instance Axios pour l'API de recherche juridique
const legalResearchApi = axios.create({
  baseURL: LEGAL_RESEARCH_API_BASE_URL,
  timeout: 30000, // 30 secondes pour les recherches
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
legalResearchApi.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
legalResearchApi.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expiré ou invalide
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Types pour les requêtes et réponses
export interface SearchRequest {
  query: string;
  filters?: {
    legalDomain?: string;
    documentType?: string;
    dateRange?: {
      startDate?: string;
      endDate?: string;
    };
    language?: string;
  };
  parameters?: {
    method?: 'Keyword' | 'Semantic' | 'Hybrid' | 'FullText' | 'Fuzzy' | 'Boolean';
    limit?: number;
    minRelevanceScore?: number;
    includeHighlights?: boolean;
    includeSimilar?: boolean;
    timeoutSeconds?: number;
  };
}

export interface SearchResult {
  id: string;
  documentId: string;
  title: string;
  content: string;
  summary?: string;
  relevanceScore: number;
  documentType: string;
  legalDomain: string;
  source: {
    name: string;
    url?: string;
    authority: string;
  };
  highlights?: Array<{
    text: string;
    startPosition: number;
    endPosition: number;
    score: number;
    type: string;
  }>;
  matchedChunks?: Array<{
    chunkId: string;
    content: string;
    type: string;
    similarityScore: number;
    startPosition: number;
    endPosition: number;
  }>;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt?: string;
}

export interface SearchResponse {
  results: SearchResult[];
  totalCount: number;
  executionTimeMs: number;
  query: {
    original: string;
    processed: string;
    intent: string;
    expandedTerms?: string[];
  };
  filters: Record<string, any>;
  parameters: Record<string, any>;
  suggestions?: string[];
  facets?: Record<string, Array<{ value: string; count: number }>>;
}

export interface SearchAnalytics {
  totalSearches: number;
  successfulSearches: number;
  averageExecutionTime: number;
  averageResultCount: number;
  popularTerms: Record<string, number>;
  searchTrends: Record<string, number>;
  averageSatisfactionScore: number;
  generatedAt: string;
}

export interface UserFeedback {
  overallRating: number;
  relevanceRating: number;
  completenessRating: number;
  usefulnessRating: number;
  comments?: string;
}

// Services de l'API de recherche juridique
export const legalResearchApiService = {
  // Effectuer une recherche
  async search(request: SearchRequest): Promise<SearchResponse> {
    try {
      const response: AxiosResponse<SearchResponse> = await legalResearchApi.post('/api/search', request);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la recherche:', error);
      throw error;
    }
  },

  // Effectuer une recherche de test (sans authentification)
  async testSearch(request: SearchRequest): Promise<SearchResponse> {
    try {
      const response: AxiosResponse<SearchResponse> = await legalResearchApi.post('/api/search/test', request);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la recherche de test:', error);
      throw error;
    }
  },

  // Recherche par similarité
  async searchSimilar(documentId: string, limit: number = 10): Promise<SearchResponse> {
    try {
      const response: AxiosResponse<SearchResponse> = await legalResearchApi.get(
        `/api/search/similar/${documentId}?limit=${limit}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la recherche de documents similaires:', error);
      throw error;
    }
  },

  // Obtenir les suggestions de recherche
  async getSuggestions(query: string): Promise<string[]> {
    try {
      const response: AxiosResponse<string[]> = await legalResearchApi.get(
        `/api/search/suggestions?query=${encodeURIComponent(query)}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des suggestions:', error);
      return [];
    }
  },

  // Analyser une requête
  async analyzeQuery(query: string): Promise<any> {
    try {
      const response = await legalResearchApi.post('/api/search/analyze', { query });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'analyse de la requête:', error);
      throw error;
    }
  },

  // Obtenir l'historique des recherches
  async getSearchHistory(limit: number = 50): Promise<any[]> {
    try {
      const response = await legalResearchApi.get(`/api/search/history?limit=${limit}`);
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération de l\'historique:', error);
      return [];
    }
  },

  // Envoyer un feedback sur une recherche
  async submitFeedback(searchId: string, feedback: UserFeedback): Promise<void> {
    try {
      await legalResearchApi.post(`/api/search/${searchId}/feedback`, feedback);
    } catch (error) {
      console.error('Erreur lors de l\'envoi du feedback:', error);
      throw error;
    }
  },

  // Obtenir les analytics de recherche
  async getAnalytics(startDate?: string, endDate?: string): Promise<SearchAnalytics> {
    try {
      const params = new URLSearchParams();
      if (startDate) params.append('startDate', startDate);
      if (endDate) params.append('endDate', endDate);

      const response: AxiosResponse<SearchAnalytics> = await legalResearchApi.get(
        `/api/search/analytics?${params.toString()}`
      );
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des analytics:', error);
      throw error;
    }
  },

  // Obtenir les facettes pour les filtres
  async getFacets(): Promise<Record<string, Array<{ value: string; count: number }>>> {
    try {
      const response = await legalResearchApi.get('/api/search/facets');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des facettes:', error);
      return {};
    }
  },

  // Exporter les résultats de recherche
  async exportResults(searchId: string, format: 'pdf' | 'excel' | 'csv' = 'pdf'): Promise<Blob> {
    try {
      const response = await legalResearchApi.get(`/api/search/${searchId}/export?format=${format}`, {
        responseType: 'blob'
      });
      return response.data;
    } catch (error) {
      console.error('Erreur lors de l\'export:', error);
      throw error;
    }
  },

  // Sauvegarder une recherche
  async saveSearch(searchId: string, name: string): Promise<void> {
    try {
      await legalResearchApi.post(`/api/search/${searchId}/save`, { name });
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      throw error;
    }
  },

  // Obtenir les recherches sauvegardées
  async getSavedSearches(): Promise<any[]> {
    try {
      const response = await legalResearchApi.get('/api/search/saved');
      return response.data;
    } catch (error) {
      console.error('Erreur lors de la récupération des recherches sauvegardées:', error);
      return [];
    }
  }
};

export default legalResearchApiService;
