{"ConnectionStrings": {"PostgreSql": "Host=localhost;Port=5433;Database=legal_research_db;Username=lexai_user;Password=lexai_password_2024!"}, "JwtSettings": {"SecretKey": "your-super-secret-jwt-key-that-is-at-least-32-characters-long", "Issuer": "LexAI.Identity.API", "Audience": "LexAI.Identity.API", "AccessTokenExpirationMinutes": 60, "RefreshTokenExpirationDays": 7, "ClockSkewMinutes": 5, "ValidateIssuer": true, "ValidateAudience": true, "ValidateLifetime": true, "ValidateIssuerSigningKey": true, "RequireHttpsMetadata": false}, "DataProcessing": {"BaseUrl": "http://localhost:5001"}, "Qdrant": {"DefaultCollection": "legal-documents", "Host": "localhost", "Port": 6333, "ApiKey": ""}, "OpenAI": {"ApiKey": "your-openai-api-key-here", "EmbeddingModel": "text-embedding-3-small", "EmbeddingDimensions": 1536, "MaxTokens": 8192, "Temperature": 0.1}, "LocalLLM": {"BaseUrl": "http://localhost:11434", "EmbeddingModel": "nomic-embed-text", "EmbeddingDimensions": 768, "ChatModel": "llama3.2:3b"}, "Search": {"DefaultLimit": 20, "MaxLimit": 100, "MinRelevanceScore": 0.5, "CacheExpirationMinutes": 30, "EnableCaching": true}, "Cors": {"AllowedOrigins": ["http://localhost:5173", "http://localhost:3000"]}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "LexAI.LegalResearch": "Debug"}}}