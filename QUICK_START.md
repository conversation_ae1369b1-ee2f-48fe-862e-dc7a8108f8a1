# 🚀 Guide de Démarrage Rapide - LexAI

Assistant juridique intelligent avec IA/NLP, recherche vectorielle et génération de documents.

## 📋 Prérequis

### Logiciels requis
- **Docker Desktop** 4.20+ avec Docker Compose
- **Node.js** 18+ et **npm** 9+
- **Git** pour cloner le repository
- **PowerShell** 7+ (Windows) ou **Bash** (Linux/macOS)

### Comptes et clés API (optionnel)
- **OpenAI API Key** (pour production) - [Obtenir une clé](https://platform.openai.com/api-keys)
- **Alternative locale** : Ollama avec llama3.2:3b (gratuit)

## ⚡ Démarrage en 5 minutes

### 1. <PERSON><PERSON><PERSON> et configurer le projet

```bash
# Cloner le repository
git clone <repository-url>
cd LexIA/V1

# Configurer les variables d'environnement
cp LexAi_Backend_V1/.env.example LexAi_Backend_V1/.env
```

### 2. Configurer les clés API

Éditez `LexAi_Backend_V1/.env` :

```env
# OpenAI (optionnel - pour production)
OPENAI_API_KEY=your-openai-api-key-here

# Ou utilisez les modèles locaux (gratuit)
USE_LOCAL_MODELS=true
LOCAL_EMBEDDING_URL=http://localhost:8000
```

### 3. Démarrer l'infrastructure

```powershell
# Windows PowerShell
cd LexAi_Backend_V1
.\scripts\start-infrastructure.ps1

# Linux/macOS
cd LexAi_Backend_V1
chmod +x scripts/start-infrastructure.sh
./scripts/start-infrastructure.sh
```

### 4. Démarrer le service d'embedding local (gratuit)

```bash
# Terminal séparé
cd LexAi_Local_Embeding
pip install -r requirements.txt
python app.py
```

### 5. Démarrer le frontend

```bash
# Terminal séparé
cd LexAi_Frontend_V1
npm install
npm run dev
```

### 6. Accéder à l'application

🌐 **Frontend** : http://localhost:3000
📊 **Mongo Express** : http://localhost:8081 (admin/mongoexpress_2024!)
🐰 **RabbitMQ** : http://localhost:15672 (lexai_user/lexai_rabbitmq_password_2024!)
🔍 **Qdrant** : http://localhost:6333/dashboard

## 🧪 Tester l'installation

```powershell
# Tester l'intégration complète
cd LexAi_Backend_V1
.\scripts\test-integration.ps1
```

## 🎯 Fonctionnalités disponibles

### ✅ Modules implémentés

1. **🔐 Authentification & Autorisation**
   - Inscription/Connexion sécurisée
   - Gestion des rôles (Admin, Avocat, Assistant, Client)
   - JWT avec refresh tokens

2. **💬 Assistant IA Juridique**
   - Chat intelligent avec GPT-4 ou modèles locaux
   - Conversations contextuelles
   - Historique des échanges

3. **🔍 Recherche Juridique (RAG)**
   - Recherche vectorielle dans la jurisprudence
   - Citations et sources automatiques
   - Filtres par domaine et juridiction

4. **📄 Analyse de Documents**
   - Upload et analyse automatique
   - Détection des clauses à risque
   - Recommandations juridiques

5. **📝 Génération de Documents**
   - Contrats, lettres, mises en demeure
   - Templates personnalisables
   - Export PDF/DOCX

6. **⚙️ Traitement de Données**
   - Pipeline automatisé avec agents IA
   - Chunking et vectorisation
   - Qualité et classification

### 🚧 En développement

- **📧 Veille Juridique** - Alertes automatiques
- **👥 Gestion Clients/Dossiers** - CRM juridique
- **📅 Agenda & Notifications** - Planification
- **✍️ Signature Électronique** - DocuSign/Yousign
- **💰 Facturation & Paiement** - Stripe/PayPal

## 🏗️ Architecture

```
LexAI/
├── 🖥️  LexAi_Frontend_V1/          # React + TailwindCSS + Shadcn
├── ⚙️  LexAi_Backend_V1/            # .NET 9 Microservices
│   ├── 🔐 Identity Service         # Auth JWT + RBAC
│   ├── 🤖 AIAssistant Service      # Chat IA + OpenAI
│   ├── 🔍 LegalResearch Service    # RAG + Qdrant
│   └── 📊 DataProcessing Service   # Pipeline IA
├── 🧠 LexAi_Local_Embeding/        # Service embeddings local
└── 🐳 Docker Infrastructure        # PostgreSQL + MongoDB + Qdrant + Redis
```

## 🔧 Configuration avancée

### Modèles IA supportés

**Production (payant)**
- OpenAI GPT-4 (chat)
- OpenAI text-embedding-ada-002 (embeddings)

**Développement (gratuit)**
- Ollama llama3.2:3b (chat local)
- SentenceTransformers (embeddings local)

### Base de données

- **PostgreSQL** : Données relationnelles (utilisateurs, conversations)
- **MongoDB** : Documents et métadonnées
- **Qdrant** : Vecteurs et recherche sémantique
- **Redis** : Cache et sessions

### Variables d'environnement importantes

```env
# Mode de fonctionnement
ASPNETCORE_ENVIRONMENT=Development
USE_LOCAL_MODELS=true

# IA et NLP
OPENAI_API_KEY=sk-...
LOCAL_EMBEDDING_URL=http://localhost:8000
OLLAMA_URL=http://localhost:11434

# Bases de données
ConnectionStrings__PostgreSql=Host=localhost;Database=lexai_db;Username=lexai_user;Password=lexai_password_2024!
ConnectionStrings__MongoDB=mongodb://lexai_admin:lexai_mongo_password_2024!@localhost:27017/lexai_documents?authSource=admin
ConnectionStrings__Qdrant=http://localhost:6333

# Sécurité
JWT_SECRET_KEY=your-super-secret-jwt-key-that-is-at-least-32-characters-long
JWT_ISSUER=LexAI
JWT_AUDIENCE=LexAI-Users
```

## 🐛 Dépannage

### Problèmes courants

**🔴 Services Docker ne démarrent pas**
```bash
# Vérifier l'état
docker compose ps

# Voir les logs
docker compose logs [service-name]

# Redémarrer un service
docker compose restart [service-name]
```

**🔴 Frontend ne se connecte pas au backend**
- Vérifiez que les services backend sont démarrés
- Contrôlez les URLs dans `LexAi_Frontend_V1/src/services/api.ts`

**🔴 Erreurs d'embedding**
- Démarrez le service local : `cd LexAi_Local_Embeding && python app.py`
- Ou configurez OpenAI dans `.env`

**🔴 Erreurs de base de données**
- Vérifiez les mots de passe dans `.env`
- Recréez les volumes : `docker compose down -v && docker compose up -d`

### Logs utiles

```bash
# Logs de tous les services
docker compose logs -f

# Logs d'un service spécifique
docker compose logs -f postgres
docker compose logs -f qdrant

# Logs du frontend
cd LexAi_Frontend_V1
npm run dev

# Logs du service d'embedding
cd LexAi_Local_Embeding
python app.py
```

## 📚 Documentation complète

- **Backend** : `LexAi_Backend_V1/README.md`
- **Frontend** : `LexAi_Frontend_V1/README.md`
- **Embedding** : `LexAi_Local_Embeding/README_LOCAL_EMBEDDING.md`
- **API** : http://localhost:8080/swagger (quand les services sont démarrés)

## 🤝 Support

1. **Issues GitHub** : Signalez les bugs et demandes de fonctionnalités
2. **Documentation** : Consultez les README de chaque service
3. **Logs** : Utilisez les scripts de diagnostic fournis

## 🎉 Prêt à utiliser !

Votre assistant juridique IA est maintenant opérationnel ! 

👤 **Créez un compte** sur http://localhost:3000
🤖 **Testez le chat IA** avec des questions juridiques
📄 **Analysez un document** en l'uploadant
🔍 **Effectuez une recherche** juridique
📝 **Générez un document** personnalisé

---

**🚀 Bon développement avec LexAI !**
