{"openapi": "3.0.1", "info": {"title": "LexAI Legal Research Service API", "description": "Service de recherche juridique avec IA pour LexAI", "contact": {"name": "LexAI Support", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/Search/search": {"post": {"tags": ["Search"], "summary": "Performs a legal research search", "requestBody": {"description": "Search request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchRequestDto"}}}}, "responses": {"200": {"description": "Search completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResponseDto"}}}}, "400": {"description": "Invalid search request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Search/hybrid-search": {"post": {"tags": ["Search"], "summary": "Performs a hybrid search combining semantic and keyword search", "requestBody": {"description": "Search request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchRequestDto"}}}}, "responses": {"200": {"description": "Hybrid search completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResponseDto"}}}}, "400": {"description": "Invalid search request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Search/similar/{documentId}": {"get": {"tags": ["Search"], "summary": "Finds documents similar to a given document", "parameters": [{"name": "documentId", "in": "path", "description": "Document ID", "required": true, "schema": {"type": "string", "format": "uuid"}}, {"name": "limit", "in": "query", "description": "Maximum number of similar documents (default: 10)", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Similar documents found", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SearchResultDto"}}}}}, "404": {"description": "Document not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Search/suggestions": {"get": {"tags": ["Search"], "summary": "Gets search suggestions based on partial query", "parameters": [{"name": "q", "in": "query", "description": "Partial query text", "schema": {"type": "string"}}, {"name": "limit", "in": "query", "description": "Maximum number of suggestions (default: 10)", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Suggestions retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}, "400": {"description": "Invalid request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Search/analyze": {"post": {"tags": ["Search"], "summary": "Analyzes a search query to extract intent and entities", "requestBody": {"description": "Query text to analyze", "content": {"application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}, "application/*+json": {"schema": {"type": "string"}}}}, "responses": {"200": {"description": "Query analyzed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QueryAnalysisDto"}}}}, "400": {"description": "Invalid query", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Search/feedback/{queryId}": {"post": {"tags": ["Search"], "summary": "Provides feedback on search results", "parameters": [{"name": "queryId", "in": "path", "description": "Search query ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "User feedback", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserFeedbackDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserFeedbackDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserFeedbackDto"}}}}, "responses": {"200": {"description": "Feedback recorded successfully", "content": {"application/json": {"schema": {}}}}, "400": {"description": "Invalid feedback", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "404": {"description": "Query not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Search/analytics": {"get": {"tags": ["Search"], "summary": "Gets search analytics for the current user", "parameters": [{"name": "sessionId", "in": "query", "description": "Optional session ID filter", "schema": {"type": "string"}}], "responses": {"200": {"description": "Analytics retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchAnalyticsDto"}}}}, "401": {"description": "User not authenticated", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/Search/test": {"post": {"tags": ["Search"], "summary": "Test endpoint for development - performs a search without authentication", "requestBody": {"description": "Search request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchRequestDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SearchRequestDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SearchRequestDto"}}}}, "responses": {"200": {"description": "Search completed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SearchResponseDto"}}}}, "400": {"description": "Invalid search request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}}, "components": {"schemas": {"AuthorityLevel": {"enum": [0, 1, 2, 3, 4, 5, 6, 7], "type": "integer", "format": "int32"}, "ChunkType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13], "type": "integer", "format": "int32"}, "DateRangeDto": {"type": "object", "properties": {"startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "DocumentSourceDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "url": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/SourceType"}, "authority": {"$ref": "#/components/schemas/AuthorityLevel"}, "jurisdiction": {"type": "string", "nullable": true}, "reliabilityScore": {"type": "number", "format": "double"}}, "additionalProperties": false}, "DocumentType": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "type": "integer", "format": "int32"}, "LegalDomain": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20], "type": "integer", "format": "int32"}, "LegalEntityDto": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "startPosition": {"type": "integer", "format": "int32"}, "endPosition": {"type": "integer", "format": "int32"}, "confidence": {"type": "number", "format": "double"}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "MatchedChunkDto": {"type": "object", "properties": {"chunkId": {"type": "string", "format": "uuid"}, "content": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/ChunkType"}, "similarityScore": {"type": "number", "format": "double"}, "startPosition": {"type": "integer", "format": "int32"}, "endPosition": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "QueryAnalysisDto": {"type": "object", "properties": {"originalQuery": {"type": "string", "nullable": true}, "processedQuery": {"type": "string", "nullable": true}, "intent": {"$ref": "#/components/schemas/QueryIntent"}, "entities": {"type": "array", "items": {"$ref": "#/components/schemas/LegalEntityDto"}, "nullable": true}, "expandedTerms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "confidenceScore": {"type": "number", "format": "double"}, "suggestedFilters": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "QueryIntent": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], "type": "integer", "format": "int32"}, "SearchAnalyticsDto": {"type": "object", "properties": {"totalSearches": {"type": "integer", "format": "int32"}, "averageExecutionTime": {"type": "number", "format": "double"}, "averageResultCount": {"type": "number", "format": "double"}, "topQueries": {"type": "array", "items": {"type": "string"}, "nullable": true}, "topDomains": {"type": "object", "properties": {"Civil": {"type": "integer", "format": "int32"}, "Criminal": {"type": "integer", "format": "int32"}, "Administrative": {"type": "integer", "format": "int32"}, "Constitutional": {"type": "integer", "format": "int32"}, "Commercial": {"type": "integer", "format": "int32"}, "Labor": {"type": "integer", "format": "int32"}, "Tax": {"type": "integer", "format": "int32"}, "RealEstate": {"type": "integer", "format": "int32"}, "Family": {"type": "integer", "format": "int32"}, "IntellectualProperty": {"type": "integer", "format": "int32"}, "Environmental": {"type": "integer", "format": "int32"}, "Health": {"type": "integer", "format": "int32"}, "Immigration": {"type": "integer", "format": "int32"}, "International": {"type": "integer", "format": "int32"}, "European": {"type": "integer", "format": "int32"}, "Banking": {"type": "integer", "format": "int32"}, "Insurance": {"type": "integer", "format": "int32"}, "Technology": {"type": "integer", "format": "int32"}, "Competition": {"type": "integer", "format": "int32"}, "Consumer": {"type": "integer", "format": "int32"}, "Other": {"type": "integer", "format": "int32"}}, "additionalProperties": false, "nullable": true}, "searchTrends": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "successRate": {"type": "number", "format": "double"}, "averageSatisfactionScore": {"type": "number", "format": "double"}}, "additionalProperties": false}, "SearchFacetsDto": {"type": "object", "properties": {"documentTypes": {"type": "object", "properties": {"Law": {"type": "integer", "format": "int32"}, "Regulation": {"type": "integer", "format": "int32"}, "Jurisprudence": {"type": "integer", "format": "int32"}, "Doctrine": {"type": "integer", "format": "int32"}, "Contract": {"type": "integer", "format": "int32"}, "Form": {"type": "integer", "format": "int32"}, "Procedure": {"type": "integer", "format": "int32"}, "Analysis": {"type": "integer", "format": "int32"}, "News": {"type": "integer", "format": "int32"}, "Other": {"type": "integer", "format": "int32"}}, "additionalProperties": false, "nullable": true}, "legalDomains": {"type": "object", "properties": {"Civil": {"type": "integer", "format": "int32"}, "Criminal": {"type": "integer", "format": "int32"}, "Administrative": {"type": "integer", "format": "int32"}, "Constitutional": {"type": "integer", "format": "int32"}, "Commercial": {"type": "integer", "format": "int32"}, "Labor": {"type": "integer", "format": "int32"}, "Tax": {"type": "integer", "format": "int32"}, "RealEstate": {"type": "integer", "format": "int32"}, "Family": {"type": "integer", "format": "int32"}, "IntellectualProperty": {"type": "integer", "format": "int32"}, "Environmental": {"type": "integer", "format": "int32"}, "Health": {"type": "integer", "format": "int32"}, "Immigration": {"type": "integer", "format": "int32"}, "International": {"type": "integer", "format": "int32"}, "European": {"type": "integer", "format": "int32"}, "Banking": {"type": "integer", "format": "int32"}, "Insurance": {"type": "integer", "format": "int32"}, "Technology": {"type": "integer", "format": "int32"}, "Competition": {"type": "integer", "format": "int32"}, "Consumer": {"type": "integer", "format": "int32"}, "Other": {"type": "integer", "format": "int32"}}, "additionalProperties": false, "nullable": true}, "sources": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "years": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "tags": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}}, "additionalProperties": false}, "SearchMethod": {"enum": [0, 1, 2, 3, 4, 5], "type": "integer", "format": "int32"}, "SearchRequestDto": {"type": "object", "properties": {"query": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid"}, "sessionId": {"type": "string", "nullable": true}, "method": {"$ref": "#/components/schemas/SearchMethod"}, "domainFilter": {"$ref": "#/components/schemas/LegalDomain"}, "typeFilter": {"$ref": "#/components/schemas/DocumentType"}, "languageFilter": {"type": "string", "nullable": true}, "dateFilter": {"$ref": "#/components/schemas/DateRangeDto"}, "limit": {"type": "integer", "format": "int32"}, "offset": {"type": "integer", "format": "int32"}, "minRelevanceScore": {"type": "number", "format": "double"}, "includeHighlights": {"type": "boolean"}, "includeSimilar": {"type": "boolean"}, "sortOrder": {"$ref": "#/components/schemas/SearchSortOrder"}, "parameters": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "SearchResponseDto": {"type": "object", "properties": {"queryId": {"type": "string", "format": "uuid"}, "query": {"type": "string", "nullable": true}, "processedQuery": {"type": "string", "nullable": true}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/SearchResultDto"}, "nullable": true}, "totalResults": {"type": "integer", "format": "int32"}, "executionTimeMs": {"type": "integer", "format": "int64"}, "method": {"$ref": "#/components/schemas/SearchMethod"}, "intent": {"$ref": "#/components/schemas/QueryIntent"}, "qualityScore": {"type": "number", "format": "double"}, "isCached": {"type": "boolean"}, "suggestions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "relatedTerms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "facets": {"$ref": "#/components/schemas/SearchFacetsDto"}, "metadata": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "SearchResultDto": {"type": "object", "properties": {"documentId": {"type": "string", "format": "uuid"}, "title": {"type": "string", "nullable": true}, "summary": {"type": "string", "nullable": true}, "relevanceScore": {"type": "number", "format": "double"}, "similarityScore": {"type": "number", "format": "double"}, "keywordScore": {"type": "number", "format": "double"}, "documentType": {"$ref": "#/components/schemas/DocumentType"}, "legalDomain": {"$ref": "#/components/schemas/LegalDomain"}, "source": {"$ref": "#/components/schemas/DocumentSourceDto"}, "highlights": {"type": "array", "items": {"$ref": "#/components/schemas/TextHighlightDto"}, "nullable": true}, "matchedChunks": {"type": "array", "items": {"$ref": "#/components/schemas/MatchedChunkDto"}, "nullable": true}, "publicationDate": {"type": "string", "format": "date-time", "nullable": true}, "effectiveDate": {"type": "string", "format": "date-time", "nullable": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "documentUrl": {"type": "string", "nullable": true}, "rank": {"type": "integer", "format": "int32"}, "matchExplanation": {"type": "string", "nullable": true}, "similarDocuments": {"type": "array", "items": {"$ref": "#/components/schemas/SimilarDocumentDto"}, "nullable": true}}, "additionalProperties": false}, "SearchSortOrder": {"enum": [0, 1, 2, 3, 4], "type": "integer", "format": "int32"}, "SimilarDocumentDto": {"type": "object", "properties": {"documentId": {"type": "string", "format": "uuid"}, "title": {"type": "string", "nullable": true}, "similarityScore": {"type": "number", "format": "double"}, "documentType": {"$ref": "#/components/schemas/DocumentType"}, "legalDomain": {"$ref": "#/components/schemas/LegalDomain"}}, "additionalProperties": false}, "SourceType": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "TextHighlightDto": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "startPosition": {"type": "integer", "format": "int32"}, "endPosition": {"type": "integer", "format": "int32"}, "score": {"type": "number", "format": "double"}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserFeedbackDto": {"type": "object", "properties": {"overallRating": {"type": "integer", "format": "int32"}, "relevanceRating": {"type": "integer", "format": "int32"}, "completenessRating": {"type": "integer", "format": "int32"}, "usefulnessRating": {"type": "integer", "format": "int32"}, "comments": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}