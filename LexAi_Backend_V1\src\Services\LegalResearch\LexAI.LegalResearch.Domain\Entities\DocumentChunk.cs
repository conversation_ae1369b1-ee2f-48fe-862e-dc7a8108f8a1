using LexAI.LegalResearch.Domain.ValueObjects;

namespace LexAI.LegalResearch.Domain.Entities;

/// <summary>
/// Represents a chunk of a legal document for vector search
/// </summary>
public class DocumentChunk
{
    /// <summary>
    /// Unique identifier for the chunk
    /// </summary>
    public Guid Id { get; private set; }

    /// <summary>
    /// ID of the parent document
    /// </summary>
    public Guid DocumentId { get; private set; }

    /// <summary>
    /// Chunk content text
    /// </summary>
    public string Content { get; private set; } = string.Empty;

    /// <summary>
    /// Type of chunk (paragraph, section, etc.)
    /// </summary>
    public ChunkType Type { get; private set; }

    /// <summary>
    /// Start position in the original document
    /// </summary>
    public int StartPosition { get; private set; }

    /// <summary>
    /// End position in the original document
    /// </summary>
    public int EndPosition { get; private set; }

    /// <summary>
    /// Sequence number within the document
    /// </summary>
    public int SequenceNumber { get; private set; }

    /// <summary>
    /// Character count of the chunk
    /// </summary>
    public int CharacterCount { get; private set; }

    /// <summary>
    /// Token count (for LLM processing)
    /// </summary>
    public int TokenCount { get; private set; }

    /// <summary>
    /// Quality score of the chunk
    /// </summary>
    public double QualityScore { get; private set; }

    /// <summary>
    /// Importance score of the chunk
    /// </summary>
    public double ImportanceScore { get; private set; }

    /// <summary>
    /// Keywords extracted from the chunk
    /// </summary>
    public List<string> Keywords { get; private set; } = new();

    /// <summary>
    /// Named entities in the chunk
    /// </summary>
    public List<string> NamedEntities { get; private set; } = new();

    /// <summary>
    /// Legal domain relevance scores
    /// </summary>
    public Dictionary<LegalDomain, double> DomainRelevance { get; private set; } = new();

    /// <summary>
    /// Embedding vector for semantic search
    /// </summary>
    public float[]? EmbeddingVector { get; private set; }

    /// <summary>
    /// Embedding model used
    /// </summary>
    public string? EmbeddingModel { get; private set; }

    /// <summary>
    /// When the chunk was created
    /// </summary>
    public DateTime CreatedAt { get; private set; }

    /// <summary>
    /// When the chunk was last updated
    /// </summary>
    public DateTime? UpdatedAt { get; private set; }

    /// <summary>
    /// Navigation property to parent document
    /// </summary>
    public LegalDocument? Document { get; private set; }

    /// <summary>
    /// Private constructor for Entity Framework
    /// </summary>
    private DocumentChunk() { }

    /// <summary>
    /// Creates a new document chunk
    /// </summary>
    /// <param name="documentId">Parent document ID</param>
    /// <param name="content">Chunk content</param>
    /// <param name="type">Chunk type</param>
    /// <param name="startPosition">Start position in document</param>
    /// <param name="endPosition">End position in document</param>
    /// <param name="sequenceNumber">Sequence number</param>
    /// <returns>New document chunk</returns>
    public static DocumentChunk Create(
        Guid documentId,
        string content,
        ChunkType type,
        int startPosition,
        int endPosition,
        int sequenceNumber)
    {
        if (documentId == Guid.Empty)
            throw new ArgumentException("Document ID cannot be empty", nameof(documentId));

        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        if (startPosition < 0)
            throw new ArgumentException("Start position cannot be negative", nameof(startPosition));

        if (endPosition <= startPosition)
            throw new ArgumentException("End position must be greater than start position", nameof(endPosition));

        var chunk = new DocumentChunk
        {
            Id = Guid.NewGuid(),
            DocumentId = documentId,
            Content = content.Trim(),
            Type = type,
            StartPosition = startPosition,
            EndPosition = endPosition,
            SequenceNumber = sequenceNumber,
            CharacterCount = content.Length,
            TokenCount = EstimateTokenCount(content),
            QualityScore = 0.5, // Default, will be calculated later
            ImportanceScore = 0.5, // Default, will be calculated later
            CreatedAt = DateTime.UtcNow
        };

        return chunk;
    }

    /// <summary>
    /// Updates the chunk content
    /// </summary>
    /// <param name="content">New content</param>
    public void UpdateContent(string content)
    {
        if (string.IsNullOrWhiteSpace(content))
            throw new ArgumentException("Content cannot be empty", nameof(content));

        Content = content.Trim();
        CharacterCount = content.Length;
        TokenCount = EstimateTokenCount(content);
        UpdatedAt = DateTime.UtcNow;

        // Clear embedding as it needs to be regenerated
        EmbeddingVector = null;
        EmbeddingModel = null;
    }

    /// <summary>
    /// Sets the embedding vector for the chunk
    /// </summary>
    /// <param name="vector">Embedding vector</param>
    /// <param name="model">Model used to generate the embedding</param>
    public void SetEmbedding(float[] vector, string model)
    {
        if (vector == null || vector.Length == 0)
            throw new ArgumentException("Vector cannot be null or empty", nameof(vector));

        if (string.IsNullOrWhiteSpace(model))
            throw new ArgumentException("Model cannot be empty", nameof(model));

        EmbeddingVector = vector;
        EmbeddingModel = model;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Sets quality and importance scores
    /// </summary>
    /// <param name="qualityScore">Quality score (0-1)</param>
    /// <param name="importanceScore">Importance score (0-1)</param>
    public void SetScores(double qualityScore, double importanceScore)
    {
        if (qualityScore < 0 || qualityScore > 1)
            throw new ArgumentException("Quality score must be between 0 and 1", nameof(qualityScore));

        if (importanceScore < 0 || importanceScore > 1)
            throw new ArgumentException("Importance score must be between 0 and 1", nameof(importanceScore));

        QualityScore = qualityScore;
        ImportanceScore = importanceScore;
        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Adds keywords to the chunk
    /// </summary>
    /// <param name="keywords">Keywords to add</param>
    public void AddKeywords(IEnumerable<string> keywords)
    {
        if (keywords == null)
            return;

        foreach (var keyword in keywords.Where(k => !string.IsNullOrWhiteSpace(k)))
        {
            var normalizedKeyword = keyword.Trim().ToLowerInvariant();
            if (!Keywords.Contains(normalizedKeyword))
            {
                Keywords.Add(normalizedKeyword);
            }
        }

        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Adds named entities to the chunk
    /// </summary>
    /// <param name="entities">Named entities to add</param>
    public void AddNamedEntities(IEnumerable<string> entities)
    {
        if (entities == null)
            return;

        foreach (var entity in entities.Where(e => !string.IsNullOrWhiteSpace(e)))
        {
            var normalizedEntity = entity.Trim();
            if (!NamedEntities.Contains(normalizedEntity))
            {
                NamedEntities.Add(normalizedEntity);
            }
        }

        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Sets domain relevance scores
    /// </summary>
    /// <param name="relevanceScores">Domain relevance scores</param>
    public void SetDomainRelevance(Dictionary<LegalDomain, double> relevanceScores)
    {
        if (relevanceScores == null)
            return;

        DomainRelevance.Clear();
        foreach (var kvp in relevanceScores)
        {
            if (kvp.Value >= 0 && kvp.Value <= 1)
            {
                DomainRelevance[kvp.Key] = kvp.Value;
            }
        }

        UpdatedAt = DateTime.UtcNow;
    }

    /// <summary>
    /// Estimates token count for the content
    /// </summary>
    /// <param name="content">Content to analyze</param>
    /// <returns>Estimated token count</returns>
    private static int EstimateTokenCount(string content)
    {
        // Simple estimation: ~4 characters per token for French text
        return (int)Math.Ceiling(content.Length / 4.0);
    }

    /// <summary>
    /// Checks if the chunk has an embedding
    /// </summary>
    /// <returns>True if chunk has embedding</returns>
    public bool HasEmbedding()
    {
        return EmbeddingVector != null && EmbeddingVector.Length > 0;
    }

    /// <summary>
    /// Gets the primary legal domain for this chunk
    /// </summary>
    /// <returns>Primary legal domain</returns>
    public LegalDomain GetPrimaryDomain()
    {
        if (DomainRelevance.Count == 0)
            return LegalDomain.Other;

        return DomainRelevance.OrderByDescending(kvp => kvp.Value).First().Key;
    }
}
